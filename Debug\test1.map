******************************************************************************
            TI ARM Clang Linker PC v4.0.3                      
******************************************************************************
>> Linked Sat Aug  2 18:55:19 2025

OUTPUT FILE NAME:   <test1.out>
ENTRY POINT SYMBOL: "_c_int00_noargs"  address: 00003461


MEMORY CONFIGURATION

         name            origin    length      used     unused   attr    fill
----------------------  --------  ---------  --------  --------  ----  --------
  FLASH                 00000000   00020000  000057d0  0001a830  R  X
  SRAM                  20200000   00008000  000009b7  00007649  RW X
  BCR_CONFIG            41c00000   000000ff  00000000  000000ff  R   
  BSL_CONFIG            41c00100   00000080  00000000  00000080  R   


SEGMENT ALLOCATION MAP

run origin  load origin   length   init length attrs members
----------  ----------- ---------- ----------- ----- -------
00000000    00000000    000057d0   000057d0    r-x
  00000000    00000000    000000c0   000000c0    r-- .intvecs
  000000c0    000000c0    00003c20   00003c20    r-x .text
  00003ce0    00003ce0    00001a80   00001a80    r-- .rodata
  00005760    00005760    00000070   00000070    r-- .cinit
20200000    20200000    000007ba   00000000    rw-
  20200000    20200000    0000055d   00000000    rw- .bss
  20200560    20200560    0000025a   00000000    rw- .data
20207e00    20207e00    00000200   00000000    rw-
  20207e00    20207e00    00000200   00000000    rw- .stack


SECTION ALLOCATION MAP

 output                                  attributes/
section   page    origin      length       input sections
--------  ----  ----------  ----------   ----------------
.intvecs   0    00000000    000000c0     
                  00000000    000000c0     startup_mspm0g350x_ticlang.o (.intvecs)

.text      0    000000c0    00003c20     
                  000000c0    000001d0     oled.o (.text.OLED_ShowChar)
                  00000290    000001cc     motor.o (.text.Motor_Square_Corner_Control)
                  0000045c    00000194     ti_msp_dl_config.o (.text.SYSCFG_DL_GPIO_init)
                  000005f0    00000192     libclang_rt.builtins.a : adddf3.S.obj (.text.adddf3_subdf3)
                  00000782    00000002                            : aeabi_div0.c.obj (.text.__aeabi_idiv0)
                  00000784    00000188     No_Mcu_Ganv_Grayscale_Sensor.o (.text.No_MCU_Ganv_Sensor_Init)
                  0000090c    0000016c     motor.o (.text.Set_PWM)
                  00000a78    0000015c     key.o (.text.Key_Scan_Debounce)
                  00000bd4    00000154     Ganway_Optimized.o (.text.Track_Basic_Control)
                  00000d28    0000014c     empty.o (.text.main)
                  00000e74    00000120     encoder.o (.text.GROUP1_IRQHandler)
                  00000f94    00000110     motor.o (.text.Motor_Smooth_Control)
                  000010a4    0000010c     libclang_rt.builtins.a : divdf3.S.obj (.text.__divdf3)
                  000011b0    00000104     driverlib.a : dl_timer.o (.text.DL_Timer_initFourCCPWMMode)
                  000012b4    00000100     empty.o (.text.TIMG0_IRQHandler)
                  000013b4    000000e8     driverlib.a : dl_timer.o (.text.DL_Timer_initTimerMode)
                  0000149c    000000e4     libclang_rt.builtins.a : muldf3.S.obj (.text.__muldf3)
                  00001580    000000e2     oled.o (.text.OLED_ShowNum)
                  00001662    000000de     oled.o (.text.OLED_Init)
                  00001740    000000dc     driverlib.a : dl_sysctl_mspm0g1x0x_g3x0x.o (.text.DL_SYSCTL_configSYSPLL)
                  0000181c    000000d8     libclang_rt.builtins.a : addsf3.S.obj (.text)
                  000018f4    000000d0     No_Mcu_Ganv_Grayscale_Sensor.o (.text.Get_Analog_value)
                  000019c4    000000b8     Ganway_Optimized.o (.text.Analyze_Track_State)
                  00001a7c    000000b8     motor.o (.text.Motor_PID_Control)
                  00001b34    000000aa     No_Mcu_Ganv_Grayscale_Sensor.o (.text.normalizeAnalogValues)
                  00001bde    00000002     --HOLE-- [fill = 0]
                  00001be0    000000a8     Ganway_Optimized.o (.text.Calculate_Line_Position)
                  00001c88    000000a8     motor.o (.text.Motor_Speed_Monitor)
                  00001d30    0000009a     oled.o (.text.OLED_ShowSignedNum)
                  00001dca    0000009a     oled.o (.text.OLED_ShowString)
                  00001e64    00000090     oled.o (.text.OLED_DrawPoint)
                  00001ef4    0000008c     ti_msp_dl_config.o (.text.SYSCFG_DL_PWM_0_init)
                  00001f80    0000008c     ti_msp_dl_config.o (.text.SYSCFG_DL_initPower)
                  0000200c    0000008c     libclang_rt.builtins.a : mulsf3.S.obj (.text.__mulsf3)
                  00002098    00000084     oled.o (.text.OLED_Refresh)
                  0000211c    00000084     Ganway_Optimized.o (.text.Way_Optimized)
                  000021a0    00000084     ti_msp_dl_config.o (.text.__NVIC_SetPriority)
                  00002224    00000082     libclang_rt.builtins.a : divsf3.S.obj (.text.__divsf3)
                  000022a6    00000002     --HOLE-- [fill = 0]
                  000022a8    0000007c     libc.a : copy_decompress_lzss.c.obj (.text:decompress:lzss)
                  00002324    00000074     motor.o (.text.Motor_Verify_Speed_Consistency)
                  00002398    00000074     Ganway_Optimized.o (.text.Track_Adaptive_Control)
                  0000240c    00000074     libclang_rt.builtins.a : comparedf2.c.obj (.text.__gedf2)
                  00002480    00000072     No_Mcu_Ganv_Grayscale_Sensor.o (.text.No_MCU_Ganv_Sensor_Init_Frist)
                  000024f2    00000002     --HOLE-- [fill = 0]
                  000024f4    00000070     Ganway_Optimized.o (.text.Track_Init)
                  00002564    0000006c     oled.o (.text.OLED_WR_Byte)
                  000025d0    0000006c     No_Mcu_Ganv_Grayscale_Sensor.o (.text.convertAnalogToDigital)
                  0000263c    00000068     Ganway_Optimized.o (.text.Handle_Lost_Line)
                  000026a4    00000068     key.o (.text.Key_1)
                  0000270c    00000068     libclang_rt.builtins.a : comparedf2.c.obj (.text.__ledf2)
                  00002774    00000062                            : aeabi_dcmp.S.obj (.text.__aeabi_dcmp)
                  000027d6    00000002     --HOLE-- [fill = 0]
                  000027d8    00000062                            : aeabi_fcmp.S.obj (.text.__aeabi_fcmp)
                  0000283a    00000002     --HOLE-- [fill = 0]
                  0000283c    00000060     oled.o (.text.OLED_Clear)
                  0000289c    0000005e     driverlib.a : dl_i2c.o (.text.DL_I2C_fillControllerTXFIFO)
                  000028fa    00000002     --HOLE-- [fill = 0]
                  000028fc    00000058     ti_msp_dl_config.o (.text.SYSCFG_DL_I2C_OLED_init)
                  00002954    00000054     ti_msp_dl_config.o (.text.SYSCFG_DL_UART_0_init)
                  000029a8    00000050     oled.o (.text.DL_I2C_startControllerTransfer)
                  000029f8    00000050     ti_msp_dl_config.o (.text.SysTick_Config)
                  00002a48    0000004c     empty.o (.text.DL_ADC12_initSingleSample)
                  00002a94    0000004c     ti_msp_dl_config.o (.text.DL_UART_setBaudRateDivisor)
                  00002ae0    0000004a     ti_msp_dl_config.o (.text.DL_ADC12_configConversionMem)
                  00002b2a    00000002     --HOLE-- [fill = 0]
                  00002b2c    0000004a     libclang_rt.builtins.a : fixdfsi.S.obj (.text.__fixdfsi)
                  00002b76    0000004a     No_Mcu_Ganv_Grayscale_Sensor.o (.text.adc_getValue)
                  00002bc0    00000048     driverlib.a : dl_uart.o (.text.DL_UART_init)
                  00002c08    00000048     oled.o (.text.OLED_DisplayTurn)
                  00002c50    00000048     ti_msp_dl_config.o (.text.SYSCFG_DL_ADC12_0_init)
                  00002c98    00000048     ti_msp_dl_config.o (.text.SYSCFG_DL_SYSCTL_init)
                  00002ce0    00000044     driverlib.a : dl_sysctl_mspm0g1x0x_g3x0x.o (.text.DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK)
                  00002d24    00000042     No_Mcu_Ganv_Grayscale_Sensor.o (.text.No_Mcu_Ganv_Sensor_Task_Without_tick)
                  00002d66    00000002     --HOLE-- [fill = 0]
                  00002d68    00000040     driverlib.a : dl_adc12.o (.text.DL_ADC12_setClockConfig)
                  00002da8    00000040     key.o (.text.Key)
                  00002de8    00000040     Ganway_Optimized.o (.text.Track_Square_Corner_Control)
                  00002e28    00000040     bsp_usart.o (.text.UART0_IRQHandler)
                  00002e68    00000040     libclang_rt.builtins.a : aeabi_uidivmod.S.obj (.text.__aeabi_uidivmod)
                  00002ea8    0000003c     ti_msp_dl_config.o (.text.DL_GPIO_initPeripheralInputFunctionFeatures)
                  00002ee4    0000003c     ti_msp_dl_config.o (.text.DL_Timer_setCounterControl)
                  00002f20    0000003c     No_Mcu_Ganv_Grayscale_Sensor.o (.text.Get_Anolog_Value)
                  00002f5c    0000003c     Ganway_Optimized.o (.text.Track_PID_Control)
                  00002f98    0000003c     Ganway_Optimized.o (.text.Track_Weighted_Control)
                  00002fd4    0000003c     libclang_rt.builtins.a : floatsisf.S.obj (.text.__floatsisf)
                  00003010    0000003c                            : comparesf2.S.obj (.text.__gtsf2)
                  0000304c    0000003c     libc.a : autoinit.c.obj (.text:__TI_auto_init_nobinit_nopinit)
                  00003088    0000003a     libclang_rt.builtins.a : comparesf2.S.obj (.text.__eqsf2)
                  000030c2    00000002     --HOLE-- [fill = 0]
                  000030c4    0000003a                            : muldsi3.S.obj (.text.__muldsi3)
                  000030fe    00000002     --HOLE-- [fill = 0]
                  00003100    00000038                            : fixsfsi.S.obj (.text.__fixsfsi)
                  00003138    00000034     ti_msp_dl_config.o (.text.DL_GPIO_initDigitalInputFeatures)
                  0000316c    00000034     oled.o (.text.OLED_ColorTurn)
                  000031a0    00000034     ti_msp_dl_config.o (.text.SYSCFG_DL_TIMER_0_init)
                  000031d4    00000034     ti_msp_dl_config.o (.text.SYSCFG_DL_init)
                  00003208    00000030     No_Mcu_Ganv_Grayscale_Sensor.o (.text.DL_ADC12_getMemResult)
                  00003238    00000030     oled.o (.text.OLED_Pow)
                  00003268    00000030     systick.o (.text.SysTick_Handler)
                  00003298    0000002c     empty.o (.text.__NVIC_ClearPendingIRQ)
                  000032c4    0000002c     empty.o (.text.__NVIC_EnableIRQ)
                  000032f0    0000002c     libclang_rt.builtins.a : floatsidf.S.obj (.text.__floatsidf)
                  0000331c    0000002c     libc.a : strncpy.c.obj (.text.strncpy)
                  00003348    00000028     Ganway_Optimized.o (.text.Calculate_Position_Error)
                  00003370    00000028     empty.o (.text.DL_Common_updateReg)
                  00003398    00000028     oled.o (.text.DL_Common_updateReg)
                  000033c0    00000028     ti_msp_dl_config.o (.text.DL_Common_updateReg)
                  000033e8    00000028     ti_msp_dl_config.o (.text.DL_I2C_setControllerRXFIFOThreshold)
                  00003410    00000028     ti_msp_dl_config.o (.text.DL_I2C_setControllerTXFIFOThreshold)
                  00003438    00000028     libclang_rt.builtins.a : floatunsisf.S.obj (.text.__floatunsisf)
                  00003460    00000028     libc.a : boot_cortex_m.c.obj (.text:_c_int00_noargs)
                  00003488    00000026     ti_msp_dl_config.o (.text.DL_I2C_setAnalogGlitchFilterPulseWidth)
                  000034ae    00000026     driverlib.a : dl_i2c.o (.text.DL_I2C_setClockConfig)
                  000034d4    00000024     motor.o (.text.Left_Control)
                  000034f8    00000024     motor.o (.text.Left_Little_Control)
                  0000351c    00000024     motor.o (.text.Right_Control)
                  00003540    00000024     motor.o (.text.Right_Little_Control)
                  00003564    00000024     libclang_rt.builtins.a : floatunsidf.S.obj (.text.__floatunsidf)
                  00003588    00000020     ti_msp_dl_config.o (.text.DL_GPIO_initPeripheralInputFunction)
                  000035a8    00000020     ti_msp_dl_config.o (.text.DL_SYSCTL_setFlashWaitState)
                  000035c8    00000020     motor.o (.text.Motor_Reset_Speed_Monitor)
                  000035e8    00000020     systick.o (.text.delay_ms)
                  00003608    0000001e     ti_msp_dl_config.o (.text.DL_ADC12_setPowerDownMode)
                  00003626    0000001e     ti_msp_dl_config.o (.text.DL_UART_setOversampling)
                  00003644    0000001c     No_Mcu_Ganv_Grayscale_Sensor.o (.text.DL_ADC12_startConversion)
                  00003660    0000001c     No_Mcu_Ganv_Grayscale_Sensor.o (.text.DL_ADC12_stopConversion)
                  0000367c    0000001c     encoder.o (.text.DL_GPIO_clearInterruptStatus)
                  00003698    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_clearInterruptStatus)
                  000036b4    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_enableHiZ)
                  000036d0    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_enableInterrupt)
                  000036ec    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_initPeripheralOutputFunction)
                  00003708    0000001c     ti_msp_dl_config.o (.text.DL_SYSCTL_setSYSOSCFreq)
                  00003724    0000001c     ti_msp_dl_config.o (.text.DL_SYSCTL_setULPCLKDivider)
                  00003740    0000001c     ti_msp_dl_config.o (.text.DL_Timer_enableInterrupt)
                  0000375c    0000001c     driverlib.a : dl_timer.o (.text.DL_Timer_setCaptCompUpdateMethod)
                  00003778    0000001c                 : dl_timer.o (.text.DL_Timer_setClockConfig)
                  00003794    0000001c     ti_msp_dl_config.o (.text.DL_UART_enableInterrupt)
                  000037b0    00000018     ti_msp_dl_config.o (.text.DL_ADC12_enablePower)
                  000037c8    00000018     ti_msp_dl_config.o (.text.DL_ADC12_reset)
                  000037e0    00000018     ti_msp_dl_config.o (.text.DL_ADC12_setSampleTime0)
                  000037f8    00000018     ti_msp_dl_config.o (.text.DL_GPIO_enableOutput)
                  00003810    00000018     ti_msp_dl_config.o (.text.DL_GPIO_enablePower)
                  00003828    00000018     encoder.o (.text.DL_GPIO_getEnabledInterruptStatus)
                  00003840    00000018     ti_msp_dl_config.o (.text.DL_GPIO_initDigitalOutput)
                  00003858    00000018     ti_msp_dl_config.o (.text.DL_GPIO_reset)
                  00003870    00000018     No_Mcu_Ganv_Grayscale_Sensor.o (.text.DL_GPIO_setPins)
                  00003888    00000018     empty.o (.text.DL_GPIO_setPins)
                  000038a0    00000018     motor.o (.text.DL_GPIO_setPins)
                  000038b8    00000018     ti_msp_dl_config.o (.text.DL_GPIO_setUpperPinsPolarity)
                  000038d0    00000018     empty.o (.text.DL_GPIO_togglePins)
                  000038e8    00000018     ti_msp_dl_config.o (.text.DL_I2C_enableAnalogGlitchFilter)
                  00003900    00000018     ti_msp_dl_config.o (.text.DL_I2C_enableController)
                  00003918    00000018     ti_msp_dl_config.o (.text.DL_I2C_enableControllerClockStretching)
                  00003930    00000018     ti_msp_dl_config.o (.text.DL_I2C_enablePower)
                  00003948    00000018     ti_msp_dl_config.o (.text.DL_I2C_reset)
                  00003960    00000018     ti_msp_dl_config.o (.text.DL_I2C_setTimerPeriod)
                  00003978    00000018     ti_msp_dl_config.o (.text.DL_SYSCTL_setBORThreshold)
                  00003990    00000018     ti_msp_dl_config.o (.text.DL_Timer_enablePower)
                  000039a8    00000018     ti_msp_dl_config.o (.text.DL_Timer_reset)
                  000039c0    00000018     driverlib.a : dl_timer.o (.text.DL_Timer_setCaptureCompareOutCtl)
                  000039d8    00000018     empty.o (.text.DL_Timer_startCounter)
                  000039f0    00000018     ti_msp_dl_config.o (.text.DL_UART_enablePower)
                  00003a08    00000018     ti_msp_dl_config.o (.text.DL_UART_reset)
                  00003a20    00000018     Ganway_Optimized.o (.text.Handle_Intersection)
                  00003a38    00000016     No_Mcu_Ganv_Grayscale_Sensor.o (.text.DL_ADC12_disableConversions)
                  00003a4e    00000016     No_Mcu_Ganv_Grayscale_Sensor.o (.text.DL_ADC12_enableConversions)
                  00003a64    00000016     ti_msp_dl_config.o (.text.DL_ADC12_enableConversions)
                  00003a7a    00000016     encoder.o (.text.DL_GPIO_readPins)
                  00003a90    00000016     key.o (.text.DL_GPIO_readPins)
                  00003aa6    00000016     ti_msp_dl_config.o (.text.DL_UART_enable)
                  00003abc    00000014     No_Mcu_Ganv_Grayscale_Sensor.o (.text.DL_GPIO_clearPins)
                  00003ad0    00000014     empty.o (.text.DL_GPIO_clearPins)
                  00003ae4    00000014     motor.o (.text.DL_GPIO_clearPins)
                  00003af8    00000014     ti_msp_dl_config.o (.text.DL_GPIO_clearPins)
                  00003b0c    00000014     oled.o (.text.DL_I2C_getControllerStatus)
                  00003b20    00000014     ti_msp_dl_config.o (.text.DL_I2C_resetControllerTransfer)
                  00003b34    00000014     ti_msp_dl_config.o (.text.DL_SYSCTL_disableSYSPLL)
                  00003b48    00000014     ti_msp_dl_config.o (.text.DL_Timer_enableClock)
                  00003b5c    00000014     ti_msp_dl_config.o (.text.DL_Timer_setCCPDirection)
                  00003b70    00000014     bsp_usart.o (.text.DL_UART_receiveData)
                  00003b84    00000014     key.o (.text.Key_Init_Debounce)
                  00003b98    00000012     empty.o (.text.DL_Timer_getPendingInterrupt)
                  00003baa    00000012     bsp_usart.o (.text.DL_UART_getPendingInterrupt)
                  00003bbc    00000012     driverlib.a : dl_uart.o (.text.DL_UART_setClockConfig)
                  00003bce    00000012     libc.a : memcpy16.S.obj (.text:TI_memcpy_small)
                  00003be0    00000012            : copy_decompress_none.c.obj (.text:decompress:none)
                  00003bf2    00000010     No_Mcu_Ganv_Grayscale_Sensor.o (.text.DL_ADC12_getStatus)
                  00003c02    00000002     --HOLE-- [fill = 0]
                  00003c04    00000010     ti_msp_dl_config.o (.text.DL_SYSCTL_disableHFXT)
                  00003c14    00000010     driverlib.a : dl_timer.o (.text.DL_Timer_setCaptureCompareValue)
                  00003c24    00000010     key.o (.text.Key_System_Tick_Inc)
                  00003c34    00000010     libc.a : copy_zero_init.c.obj (.text:decompress:ZI)
                  00003c44    0000000e     No_Mcu_Ganv_Grayscale_Sensor.o (.text.Get_Digtal_For_User)
                  00003c52    0000000e     libc.a : strcpy.c.obj (.text.strcpy)
                  00003c60    0000000e            : memset16.S.obj (.text:TI_memset_small)
                  00003c6e    0000000c     ti_msp_dl_config.o (.text.SYSCFG_DL_SYSTICK_init)
                  00003c7a    00000002     --HOLE-- [fill = 0]
                  00003c7c    0000000c     libclang_rt.builtins.a : aeabi_memset.S.obj (.text.__aeabi_memclr)
                  00003c88    0000000c     systick.o (.text.get_systicks)
                  00003c94    0000000c     Scheduler.o (.text.scheduler_init)
                  00003ca0    0000000a     driverlib.a : dl_common.o (.text.DL_Common_delayCycles)
                  00003caa    00000002     --HOLE-- [fill = 0]
                  00003cac    00000008     libclang_rt.builtins.a : aeabi_memcpy.S.obj (.text.__aeabi_memcpy)
                  00003cb4    00000006     libc.a : exit.c.obj (.text:abort)
                  00003cba    00000004     startup_mspm0g350x_ticlang.o (.text.Default_Handler)
                  00003cbe    00000004     libsysbm.a : hostexit.c.obj (.text.HOSTexit)
                  00003cc2    00000004     startup_mspm0g350x_ticlang.o (.text.Reset_Handler)
                  00003cc6    00000002     --HOLE-- [fill = 0]
                  00003cc8    00000010     libc.a : boot_cortex_m.c.obj (.tramp._c_int00_noargs.1)
                  00003cd8    00000004            : pre_init.c.obj (.text._system_pre_init)
                  00003cdc    00000004     --HOLE-- [fill = 0]

.cinit     0    00005760    00000070     
                  00005760    0000004a     (.cinit..data.load) [load image, compression = lzss]
                  000057aa    00000002     --HOLE-- [fill = 0]
                  000057ac    0000000c     (__TI_handler_table)
                  000057b8    00000008     (.cinit..bss.load) [load image, compression = zero_init]
                  000057c0    00000010     (__TI_cinit_table)

.rodata    0    00003ce0    00001a80     
                  00003ce0    00000d5c     oled.o (.rodata.asc2_2412)
                  00004a3c    000005f0     oled.o (.rodata.asc2_1608)
                  0000502c    00000474     oled.o (.rodata.asc2_1206)
                  000054a0    00000228     oled.o (.rodata.asc2_0806)
                  000056c8    00000028     ti_msp_dl_config.o (.rodata.gSYSPLLConfig)
                  000056f0    00000020     Ganway_Optimized.o (.rodata.sensor_weights)
                  00005710    00000014     ti_msp_dl_config.o (.rodata.gTIMER_0TimerConfig)
                  00005724    0000000a     ti_msp_dl_config.o (.rodata.gUART_0Config)
                  0000572e    00000002     ti_msp_dl_config.o (.rodata.gI2C_OLEDClockConfig)
                  00005730    00000008     ti_msp_dl_config.o (.rodata.gADC12_0ClockConfig)
                  00005738    00000008     ti_msp_dl_config.o (.rodata.gPWM_0Config)
                  00005740    00000008     motor.o (.rodata.str1.5850567729483738290.1)
                  00005748    00000006     motor.o (.rodata.str1.10718775090649846465.1)
                  0000574e    00000003     ti_msp_dl_config.o (.rodata.gPWM_0ClockConfig)
                  00005751    00000003     ti_msp_dl_config.o (.rodata.gTIMER_0ClockConfig)
                  00005754    00000003     empty.o (.rodata.str1.9517790425240694019.1)
                  00005757    00000002     ti_msp_dl_config.o (.rodata.gUART_0ClockConfig)
                  00005759    00000007     --HOLE-- [fill = 0]

.init_array 
*          0    00000000    00000000     UNINITIALIZED

.binit     0    00000000    00000000     

.bss       0    20200000    0000055d     UNINITIALIZED
                  20200000    00000480     (.common:OLED_GRAM)
                  20200480    000000bc     (.common:gPWM_0Backup)
                  2020053c    00000004     (.common:Flag_stop)
                  20200540    00000004     (.common:Flag_stop1)
                  20200544    00000004     (.common:Get_Encoder_countA)
                  20200548    00000004     (.common:Get_Encoder_countB)
                  2020054c    00000004     (.common:encoderA_cnt)
                  20200550    00000004     (.common:encoderB_cnt)
                  20200554    00000004     (.common:gpio_interrup1)
                  20200558    00000004     (.common:gpio_interrup2)
                  2020055c    00000001     (.common:task_num)

.data      0    20200560    0000025a     UNINITIALIZED
                  20200560    00000100     empty.o (.data.rx_buff)
                  20200660    00000080     bsp_usart.o (.data.uart_rx_buffer)
                  202006e0    00000038     motor.o (.data.speed_monitor)
                  20200718    00000024     motor.o (.data.motor_pid)
                  2020073c    00000020     Ganway_Optimized.o (.data.track_ctrl)
                  2020075c    00000010     empty.o (.data.Anolog)
                  2020076c    00000010     empty.o (.data.black)
                  2020077c    00000010     empty.o (.data.white)
                  2020078c    0000000c     key.o (.data.key1_ctrl)
                  20200798    00000008     systick.o (.data.systicks)
                  202007a0    00000004     empty.o (.data.D_Num)
                  202007a4    00000004     motor.o (.data.Motor_Square_Corner_Control.last_turn_direction)
                  202007a8    00000004     motor.o (.data.Motor_Square_Corner_Control.sharp_turn_counter)
                  202007ac    00000004     empty.o (.data.Run)
                  202007b0    00000004     systick.o (.data.delay_times)
                  202007b4    00000004     key.o (.data.system_tick_ms)
                  202007b8    00000001     bsp_usart.o (.data.uart_rx_index)
                  202007b9    00000001     bsp_usart.o (.data.uart_rx_ticks)

.stack     0    20207e00    00000200     UNINITIALIZED
                  20207e00    00000004     libc.a : boot_cortex_m.c.obj (.stack)
                  20207e04    000001fc     --HOLE--

__llvm_prf_cnts 
*          0    20200000    00000000     UNINITIALIZED

__llvm_prf_bits 
*          0    20200000    00000000     UNINITIALIZED

MODULE SUMMARY

       Module                           code    ro data   rw data
       ------                           ----    -------   -------
    .\
       ti_msp_dl_config.o               2772    96        188    
       empty.o                          902     3         328    
       startup_mspm0g350x_ticlang.o     8       192       0      
    +--+--------------------------------+-------+---------+---------+
       Total:                           3682    291       516    
                                                                 
    .\app\
       motor.o                          1784    14        100    
       Ganway_Optimized.o               1404    32        32     
       No_Mcu_Ganv_Grayscale_Sensor.o   1414    0         0      
       key.o                            574     0         16     
       encoder.o                        362     0         16     
       Scheduler.o                      12      0         1      
    +--+--------------------------------+-------+---------+---------+
       Total:                           5550    46        165    
                                                                 
    .\app\OLED\
       oled.o                           2012    6632      1152   
    +--+--------------------------------+-------+---------+---------+
       Total:                           2012    6632      1152   
                                                                 
    .\bsp\
       bsp_usart.o                      102     0         130    
       systick.o                        92      0         12     
    +--+--------------------------------+-------+---------+---------+
       Total:                           194     0         142    
                                                                 
    C:/ti/mspm0_sdk_2_05_01_00/source/ti/driverlib/lib/ticlang/m0p/mspm0g1x0x_g3x0x/driverlib.a
       dl_timer.o                       588     0         0      
       dl_sysctl_mspm0g1x0x_g3x0x.o     288     0         0      
       dl_i2c.o                         132     0         0      
       dl_uart.o                        90      0         0      
       dl_adc12.o                       64      0         0      
       dl_common.o                      10      0         0      
    +--+--------------------------------+-------+---------+---------+
       Total:                           1172    0         0      
                                                                 
    C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi/c/libc.a
       copy_decompress_lzss.c.obj       124     0         0      
       autoinit.c.obj                   60      0         0      
       boot_cortex_m.c.obj              56      0         0      
       strncpy.c.obj                    44      0         0      
       copy_decompress_none.c.obj       18      0         0      
       memcpy16.S.obj                   18      0         0      
       copy_zero_init.c.obj             16      0         0      
       memset16.S.obj                   14      0         0      
       strcpy.c.obj                     14      0         0      
       exit.c.obj                       6       0         0      
       pre_init.c.obj                   4       0         0      
    +--+--------------------------------+-------+---------+---------+
       Total:                           374     0         0      
                                                                 
    C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi/c/libsysbm.a
       hostexit.c.obj                   4       0         0      
    +--+--------------------------------+-------+---------+---------+
       Total:                           4       0         0      
                                                                 
    C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi/libclang_rt.builtins.a
       adddf3.S.obj                     402     0         0      
       divdf3.S.obj                     268     0         0      
       muldf3.S.obj                     228     0         0      
       comparedf2.c.obj                 220     0         0      
       addsf3.S.obj                     216     0         0      
       mulsf3.S.obj                     140     0         0      
       divsf3.S.obj                     130     0         0      
       comparesf2.S.obj                 118     0         0      
       aeabi_dcmp.S.obj                 98      0         0      
       aeabi_fcmp.S.obj                 98      0         0      
       fixdfsi.S.obj                    74      0         0      
       aeabi_uidivmod.S.obj             64      0         0      
       floatsisf.S.obj                  60      0         0      
       muldsi3.S.obj                    58      0         0      
       fixsfsi.S.obj                    56      0         0      
       floatsidf.S.obj                  44      0         0      
       floatunsisf.S.obj                40      0         0      
       floatunsidf.S.obj                36      0         0      
       aeabi_memset.S.obj               12      0         0      
       aeabi_memcpy.S.obj               8       0         0      
       aeabi_div0.c.obj                 2       0         0      
    +--+--------------------------------+-------+---------+---------+
       Total:                           2372    0         0      
                                                                 
       Stack:                           0       0         512    
       Linker Generated:                0       110       0      
    +--+--------------------------------+-------+---------+---------+
       Grand Total:                     15360   7079      2487   


LINKER GENERATED COPY TABLES

__TI_cinit_table @ 000057c0 records: 2, size/record: 8, table size: 16
	.data: load addr=00005760, load size=0000004a bytes, run addr=20200560, run size=0000025a bytes, compression=lzss
	.bss: load addr=000057b8, load size=00000008 bytes, run addr=20200000, run size=0000055d bytes, compression=zero_init


LINKER GENERATED HANDLER TABLE

__TI_handler_table @ 000057ac records: 3, size/record: 4, table size: 12
	index: 0, handler: __TI_decompress_lzss
	index: 1, handler: __TI_decompress_none
	index: 2, handler: __TI_zero_init


FAR CALL TRAMPOLINES

callee name               trampoline name
   callee addr  tramp addr   call addr  call info
--------------  -----------  ---------  ----------------
_c_int00_noargs           $Tramp$TT$L$PI$$_c_int00_noargs
   00003461     00003cc8     00003cc2   startup_mspm0g350x_ticlang.o (.text.Reset_Handler)

[1 trampolines]
[1 trampoline calls]


GLOBAL SYMBOLS: SORTED ALPHABETICALLY BY Name 

address   name                                 
-------   ----                                 
00003cbb  ADC0_IRQHandler                      
00003cbb  ADC1_IRQHandler                      
00003cbb  AES_IRQHandler                       
000019c5  Analyze_Track_State                  
2020075c  Anolog                               
00003cbe  C$$EXIT                              
00003cbb  CANFD0_IRQHandler                    
00001be1  Calculate_Line_Position              
00003349  Calculate_Position_Error             
00003cbb  DAC0_IRQHandler                      
00002d69  DL_ADC12_setClockConfig              
00003ca1  DL_Common_delayCycles                
0000289d  DL_I2C_fillControllerTXFIFO          
000034af  DL_I2C_setClockConfig                
00001741  DL_SYSCTL_configSYSPLL               
00002ce1  DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK
000011b1  DL_Timer_initFourCCPWMMode           
000013b5  DL_Timer_initTimerMode               
0000375d  DL_Timer_setCaptCompUpdateMethod     
000039c1  DL_Timer_setCaptureCompareOutCtl     
00003c15  DL_Timer_setCaptureCompareValue      
00003779  DL_Timer_setClockConfig              
00002bc1  DL_UART_init                         
00003bbd  DL_UART_setClockConfig               
00003cbb  DMA_IRQHandler                       
202007a0  D_Num                                
00003cbb  Default_Handler                      
2020053c  Flag_stop                            
20200540  Flag_stop1                           
00003cbb  GROUP0_IRQHandler                    
00000e75  GROUP1_IRQHandler                    
000018f5  Get_Analog_value                     
00002f21  Get_Anolog_Value                     
00003c45  Get_Digtal_For_User                  
20200544  Get_Encoder_countA                   
20200548  Get_Encoder_countB                   
00003cbf  HOSTexit                             
00003a21  Handle_Intersection                  
0000263d  Handle_Lost_Line                     
00003cbb  HardFault_Handler                    
00003cbb  I2C0_IRQHandler                      
00003cbb  I2C1_IRQHandler                      
00002da9  Key                                  
000026a5  Key_1                                
00003b85  Key_Init_Debounce                    
00000a79  Key_Scan_Debounce                    
00003c25  Key_System_Tick_Inc                  
000034d5  Left_Control                         
000034f9  Left_Little_Control                  
00001a7d  Motor_PID_Control                    
000035c9  Motor_Reset_Speed_Monitor            
00000f95  Motor_Smooth_Control                 
00001c89  Motor_Speed_Monitor                  
00000291  Motor_Square_Corner_Control          
00002325  Motor_Verify_Speed_Consistency       
00003cbb  NMI_Handler                          
00000785  No_MCU_Ganv_Sensor_Init              
00002481  No_MCU_Ganv_Sensor_Init_Frist        
00002d25  No_Mcu_Ganv_Sensor_Task_Without_tick 
0000283d  OLED_Clear                           
0000316d  OLED_ColorTurn                       
00002c09  OLED_DisplayTurn                     
00001e65  OLED_DrawPoint                       
20200000  OLED_GRAM                            
00001663  OLED_Init                            
00003239  OLED_Pow                             
00002099  OLED_Refresh                         
000000c1  OLED_ShowChar                        
00001581  OLED_ShowNum                         
00001d31  OLED_ShowSignedNum                   
00001dcb  OLED_ShowString                      
00002565  OLED_WR_Byte                         
00003cbb  PendSV_Handler                       
00003cbb  RTC_IRQHandler                       
00003cc3  Reset_Handler                        
0000351d  Right_Control                        
00003541  Right_Little_Control                 
202007ac  Run                                  
00003cbb  SPI0_IRQHandler                      
00003cbb  SPI1_IRQHandler                      
00003cbb  SVC_Handler                          
00002c51  SYSCFG_DL_ADC12_0_init               
0000045d  SYSCFG_DL_GPIO_init                  
000028fd  SYSCFG_DL_I2C_OLED_init              
00001ef5  SYSCFG_DL_PWM_0_init                 
00002c99  SYSCFG_DL_SYSCTL_init                
00003c6f  SYSCFG_DL_SYSTICK_init               
000031a1  SYSCFG_DL_TIMER_0_init               
00002955  SYSCFG_DL_UART_0_init                
000031d5  SYSCFG_DL_init                       
00001f81  SYSCFG_DL_initPower                  
0000090d  Set_PWM                              
00003269  SysTick_Handler                      
00003cbb  TIMA0_IRQHandler                     
00003cbb  TIMA1_IRQHandler                     
000012b5  TIMG0_IRQHandler                     
00003cbb  TIMG12_IRQHandler                    
00003cbb  TIMG6_IRQHandler                     
00003cbb  TIMG7_IRQHandler                     
00003cbb  TIMG8_IRQHandler                     
00003bcf  TI_memcpy_small                      
00003c61  TI_memset_small                      
00002399  Track_Adaptive_Control               
00000bd5  Track_Basic_Control                  
000024f5  Track_Init                           
00002f5d  Track_PID_Control                    
00002de9  Track_Square_Corner_Control          
00002f99  Track_Weighted_Control               
00002e29  UART0_IRQHandler                     
00003cbb  UART1_IRQHandler                     
00003cbb  UART2_IRQHandler                     
00003cbb  UART3_IRQHandler                     
0000211d  Way_Optimized                        
20208000  __STACK_END                          
00000200  __STACK_SIZE                         
00000000  __TI_ATRegion0_region_sz             
00000000  __TI_ATRegion0_src_addr              
00000000  __TI_ATRegion0_trg_addr              
00000000  __TI_ATRegion1_region_sz             
00000000  __TI_ATRegion1_src_addr              
00000000  __TI_ATRegion1_trg_addr              
00000000  __TI_ATRegion2_region_sz             
00000000  __TI_ATRegion2_src_addr              
00000000  __TI_ATRegion2_trg_addr              
000057c0  __TI_CINIT_Base                      
000057d0  __TI_CINIT_Limit                     
000057d0  __TI_CINIT_Warm                      
000057ac  __TI_Handler_Table_Base              
000057b8  __TI_Handler_Table_Limit             
0000304d  __TI_auto_init_nobinit_nopinit       
000022a9  __TI_decompress_lzss                 
00003be1  __TI_decompress_none                 
ffffffff  __TI_pprof_out_hndl                  
ffffffff  __TI_prof_data_size                  
ffffffff  __TI_prof_data_start                 
00000000  __TI_static_base__                   
00003c35  __TI_zero_init                       
000005fb  __adddf3                             
00001827  __addsf3                             
00002b2d  __aeabi_d2iz                         
000005fb  __aeabi_dadd                         
00002775  __aeabi_dcmpeq                       
000027b1  __aeabi_dcmpge                       
000027c5  __aeabi_dcmpgt                       
0000279d  __aeabi_dcmple                       
00002789  __aeabi_dcmplt                       
000010a5  __aeabi_ddiv                         
0000149d  __aeabi_dmul                         
000005f1  __aeabi_dsub                         
00003101  __aeabi_f2iz                         
00001827  __aeabi_fadd                         
000027d9  __aeabi_fcmpeq                       
00002815  __aeabi_fcmpge                       
00002829  __aeabi_fcmpgt                       
00002801  __aeabi_fcmple                       
000027ed  __aeabi_fcmplt                       
00002225  __aeabi_fdiv                         
0000200d  __aeabi_fmul                         
0000181d  __aeabi_fsub                         
000032f1  __aeabi_i2d                          
00002fd5  __aeabi_i2f                          
00000783  __aeabi_idiv0                        
00003c7d  __aeabi_memclr                       
00003c7d  __aeabi_memclr4                      
00003c7d  __aeabi_memclr8                      
00003cad  __aeabi_memcpy                       
00003cad  __aeabi_memcpy4                      
00003cad  __aeabi_memcpy8                      
00003565  __aeabi_ui2d                         
00003439  __aeabi_ui2f                         
00002e69  __aeabi_uidiv                        
00002e69  __aeabi_uidivmod                     
ffffffff  __binit__                            
0000270d  __cmpdf2                             
00003089  __cmpsf2                             
000010a5  __divdf3                             
00002225  __divsf3                             
0000270d  __eqdf2                              
00003089  __eqsf2                              
00002b2d  __fixdfsi                            
00003101  __fixsfsi                            
000032f1  __floatsidf                          
00002fd5  __floatsisf                          
00003565  __floatunsidf                        
00003439  __floatunsisf                        
0000240d  __gedf2                              
00003011  __gesf2                              
0000240d  __gtdf2                              
00003011  __gtsf2                              
0000270d  __ledf2                              
00003089  __lesf2                              
0000270d  __ltdf2                              
00003089  __ltsf2                              
UNDEFED   __mpu_init                           
0000149d  __muldf3                             
000030c5  __muldsi3                            
0000200d  __mulsf3                             
0000270d  __nedf2                              
00003089  __nesf2                              
20207e00  __stack                              
20200000  __start___llvm_prf_bits              
20200000  __start___llvm_prf_cnts              
20200000  __stop___llvm_prf_bits               
20200000  __stop___llvm_prf_cnts               
000005f1  __subdf3                             
0000181d  __subsf3                             
00003461  _c_int00_noargs                      
UNDEFED   _system_post_cinit                   
00003cd9  _system_pre_init                     
00003cb5  abort                                
00002b77  adc_getValue                         
000054a0  asc2_0806                            
0000502c  asc2_1206                            
00004a3c  asc2_1608                            
00003ce0  asc2_2412                            
ffffffff  binit                                
2020076c  black                                
000025d1  convertAnalogToDigital               
000035e9  delay_ms                             
202007b0  delay_times                          
2020054c  encoderA_cnt                         
20200550  encoderB_cnt                         
20200480  gPWM_0Backup                         
00003c89  get_systicks                         
20200554  gpio_interrup1                       
20200558  gpio_interrup2                       
00000000  interruptVectors                     
2020078c  key1_ctrl                            
00000d29  main                                 
20200718  motor_pid                            
00001b35  normalizeAnalogValues                
20200560  rx_buff                              
00003c95  scheduler_init                       
00003c53  strcpy                               
0000331d  strncpy                              
2020055c  task_num                             
2020073c  track_ctrl                           
20200660  uart_rx_buffer                       
202007b8  uart_rx_index                        
202007b9  uart_rx_ticks                        
2020077c  white                                


GLOBAL SYMBOLS: SORTED BY Symbol Address 

address   name                                 
-------   ----                                 
00000000  __TI_ATRegion0_region_sz             
00000000  __TI_ATRegion0_src_addr              
00000000  __TI_ATRegion0_trg_addr              
00000000  __TI_ATRegion1_region_sz             
00000000  __TI_ATRegion1_src_addr              
00000000  __TI_ATRegion1_trg_addr              
00000000  __TI_ATRegion2_region_sz             
00000000  __TI_ATRegion2_src_addr              
00000000  __TI_ATRegion2_trg_addr              
00000000  __TI_static_base__                   
00000000  interruptVectors                     
000000c1  OLED_ShowChar                        
00000200  __STACK_SIZE                         
00000291  Motor_Square_Corner_Control          
0000045d  SYSCFG_DL_GPIO_init                  
000005f1  __aeabi_dsub                         
000005f1  __subdf3                             
000005fb  __adddf3                             
000005fb  __aeabi_dadd                         
00000783  __aeabi_idiv0                        
00000785  No_MCU_Ganv_Sensor_Init              
0000090d  Set_PWM                              
00000a79  Key_Scan_Debounce                    
00000bd5  Track_Basic_Control                  
00000d29  main                                 
00000e75  GROUP1_IRQHandler                    
00000f95  Motor_Smooth_Control                 
000010a5  __aeabi_ddiv                         
000010a5  __divdf3                             
000011b1  DL_Timer_initFourCCPWMMode           
000012b5  TIMG0_IRQHandler                     
000013b5  DL_Timer_initTimerMode               
0000149d  __aeabi_dmul                         
0000149d  __muldf3                             
00001581  OLED_ShowNum                         
00001663  OLED_Init                            
00001741  DL_SYSCTL_configSYSPLL               
0000181d  __aeabi_fsub                         
0000181d  __subsf3                             
00001827  __addsf3                             
00001827  __aeabi_fadd                         
000018f5  Get_Analog_value                     
000019c5  Analyze_Track_State                  
00001a7d  Motor_PID_Control                    
00001b35  normalizeAnalogValues                
00001be1  Calculate_Line_Position              
00001c89  Motor_Speed_Monitor                  
00001d31  OLED_ShowSignedNum                   
00001dcb  OLED_ShowString                      
00001e65  OLED_DrawPoint                       
00001ef5  SYSCFG_DL_PWM_0_init                 
00001f81  SYSCFG_DL_initPower                  
0000200d  __aeabi_fmul                         
0000200d  __mulsf3                             
00002099  OLED_Refresh                         
0000211d  Way_Optimized                        
00002225  __aeabi_fdiv                         
00002225  __divsf3                             
000022a9  __TI_decompress_lzss                 
00002325  Motor_Verify_Speed_Consistency       
00002399  Track_Adaptive_Control               
0000240d  __gedf2                              
0000240d  __gtdf2                              
00002481  No_MCU_Ganv_Sensor_Init_Frist        
000024f5  Track_Init                           
00002565  OLED_WR_Byte                         
000025d1  convertAnalogToDigital               
0000263d  Handle_Lost_Line                     
000026a5  Key_1                                
0000270d  __cmpdf2                             
0000270d  __eqdf2                              
0000270d  __ledf2                              
0000270d  __ltdf2                              
0000270d  __nedf2                              
00002775  __aeabi_dcmpeq                       
00002789  __aeabi_dcmplt                       
0000279d  __aeabi_dcmple                       
000027b1  __aeabi_dcmpge                       
000027c5  __aeabi_dcmpgt                       
000027d9  __aeabi_fcmpeq                       
000027ed  __aeabi_fcmplt                       
00002801  __aeabi_fcmple                       
00002815  __aeabi_fcmpge                       
00002829  __aeabi_fcmpgt                       
0000283d  OLED_Clear                           
0000289d  DL_I2C_fillControllerTXFIFO          
000028fd  SYSCFG_DL_I2C_OLED_init              
00002955  SYSCFG_DL_UART_0_init                
00002b2d  __aeabi_d2iz                         
00002b2d  __fixdfsi                            
00002b77  adc_getValue                         
00002bc1  DL_UART_init                         
00002c09  OLED_DisplayTurn                     
00002c51  SYSCFG_DL_ADC12_0_init               
00002c99  SYSCFG_DL_SYSCTL_init                
00002ce1  DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK
00002d25  No_Mcu_Ganv_Sensor_Task_Without_tick 
00002d69  DL_ADC12_setClockConfig              
00002da9  Key                                  
00002de9  Track_Square_Corner_Control          
00002e29  UART0_IRQHandler                     
00002e69  __aeabi_uidiv                        
00002e69  __aeabi_uidivmod                     
00002f21  Get_Anolog_Value                     
00002f5d  Track_PID_Control                    
00002f99  Track_Weighted_Control               
00002fd5  __aeabi_i2f                          
00002fd5  __floatsisf                          
00003011  __gesf2                              
00003011  __gtsf2                              
0000304d  __TI_auto_init_nobinit_nopinit       
00003089  __cmpsf2                             
00003089  __eqsf2                              
00003089  __lesf2                              
00003089  __ltsf2                              
00003089  __nesf2                              
000030c5  __muldsi3                            
00003101  __aeabi_f2iz                         
00003101  __fixsfsi                            
0000316d  OLED_ColorTurn                       
000031a1  SYSCFG_DL_TIMER_0_init               
000031d5  SYSCFG_DL_init                       
00003239  OLED_Pow                             
00003269  SysTick_Handler                      
000032f1  __aeabi_i2d                          
000032f1  __floatsidf                          
0000331d  strncpy                              
00003349  Calculate_Position_Error             
00003439  __aeabi_ui2f                         
00003439  __floatunsisf                        
00003461  _c_int00_noargs                      
000034af  DL_I2C_setClockConfig                
000034d5  Left_Control                         
000034f9  Left_Little_Control                  
0000351d  Right_Control                        
00003541  Right_Little_Control                 
00003565  __aeabi_ui2d                         
00003565  __floatunsidf                        
000035c9  Motor_Reset_Speed_Monitor            
000035e9  delay_ms                             
0000375d  DL_Timer_setCaptCompUpdateMethod     
00003779  DL_Timer_setClockConfig              
000039c1  DL_Timer_setCaptureCompareOutCtl     
00003a21  Handle_Intersection                  
00003b85  Key_Init_Debounce                    
00003bbd  DL_UART_setClockConfig               
00003bcf  TI_memcpy_small                      
00003be1  __TI_decompress_none                 
00003c15  DL_Timer_setCaptureCompareValue      
00003c25  Key_System_Tick_Inc                  
00003c35  __TI_zero_init                       
00003c45  Get_Digtal_For_User                  
00003c53  strcpy                               
00003c61  TI_memset_small                      
00003c6f  SYSCFG_DL_SYSTICK_init               
00003c7d  __aeabi_memclr                       
00003c7d  __aeabi_memclr4                      
00003c7d  __aeabi_memclr8                      
00003c89  get_systicks                         
00003c95  scheduler_init                       
00003ca1  DL_Common_delayCycles                
00003cad  __aeabi_memcpy                       
00003cad  __aeabi_memcpy4                      
00003cad  __aeabi_memcpy8                      
00003cb5  abort                                
00003cbb  ADC0_IRQHandler                      
00003cbb  ADC1_IRQHandler                      
00003cbb  AES_IRQHandler                       
00003cbb  CANFD0_IRQHandler                    
00003cbb  DAC0_IRQHandler                      
00003cbb  DMA_IRQHandler                       
00003cbb  Default_Handler                      
00003cbb  GROUP0_IRQHandler                    
00003cbb  HardFault_Handler                    
00003cbb  I2C0_IRQHandler                      
00003cbb  I2C1_IRQHandler                      
00003cbb  NMI_Handler                          
00003cbb  PendSV_Handler                       
00003cbb  RTC_IRQHandler                       
00003cbb  SPI0_IRQHandler                      
00003cbb  SPI1_IRQHandler                      
00003cbb  SVC_Handler                          
00003cbb  TIMA0_IRQHandler                     
00003cbb  TIMA1_IRQHandler                     
00003cbb  TIMG12_IRQHandler                    
00003cbb  TIMG6_IRQHandler                     
00003cbb  TIMG7_IRQHandler                     
00003cbb  TIMG8_IRQHandler                     
00003cbb  UART1_IRQHandler                     
00003cbb  UART2_IRQHandler                     
00003cbb  UART3_IRQHandler                     
00003cbe  C$$EXIT                              
00003cbf  HOSTexit                             
00003cc3  Reset_Handler                        
00003cd9  _system_pre_init                     
00003ce0  asc2_2412                            
00004a3c  asc2_1608                            
0000502c  asc2_1206                            
000054a0  asc2_0806                            
000057ac  __TI_Handler_Table_Base              
000057b8  __TI_Handler_Table_Limit             
000057c0  __TI_CINIT_Base                      
000057d0  __TI_CINIT_Limit                     
000057d0  __TI_CINIT_Warm                      
20200000  OLED_GRAM                            
20200000  __start___llvm_prf_bits              
20200000  __start___llvm_prf_cnts              
20200000  __stop___llvm_prf_bits               
20200000  __stop___llvm_prf_cnts               
20200480  gPWM_0Backup                         
2020053c  Flag_stop                            
20200540  Flag_stop1                           
20200544  Get_Encoder_countA                   
20200548  Get_Encoder_countB                   
2020054c  encoderA_cnt                         
20200550  encoderB_cnt                         
20200554  gpio_interrup1                       
20200558  gpio_interrup2                       
2020055c  task_num                             
20200560  rx_buff                              
20200660  uart_rx_buffer                       
20200718  motor_pid                            
2020073c  track_ctrl                           
2020075c  Anolog                               
2020076c  black                                
2020077c  white                                
2020078c  key1_ctrl                            
202007a0  D_Num                                
202007ac  Run                                  
202007b0  delay_times                          
202007b8  uart_rx_index                        
202007b9  uart_rx_ticks                        
20207e00  __stack                              
20208000  __STACK_END                          
ffffffff  __TI_pprof_out_hndl                  
ffffffff  __TI_prof_data_size                  
ffffffff  __TI_prof_data_start                 
ffffffff  __binit__                            
ffffffff  binit                                
UNDEFED   __mpu_init                           
UNDEFED   _system_post_cinit                   

[241 symbols]
