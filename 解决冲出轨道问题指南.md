# 解决冲出轨道问题指南

## 🚨 问题现象
- 小车在直角转弯时冲出轨道
- 速度过快导致转弯半径过大
- 即使使用精确转弯控制仍然冲出去

## 🎯 解决方案

### 方案1：使用极保守模式（推荐）
```c
// 最安全的选择，确保不冲出轨道
Ultra_Conservative_Tracking_Example(sensor);
```

**特点：**
- 直线速度：1800（原来3200）
- 转弯速度：1200（原来2400）
- 直角转弯：800（原来1600）
- 循环延时：15ms（增加响应时间）

### 方案2：使用超保守示例
```c
// 针对正方形轨道的超保守控制
Square_Track_Ultra_Conservative_Example(sensor);
```

**特点：**
- 动态速度调整
- 转弯预减速
- 丢线后极低速恢复

### 方案3：使用分段速度控制
```c
// 精确控制每个阶段的速度
Square_Track_Staged_Speed_Example(sensor);
```

**特点：**
- 转弯分阶段减速
- 状态变化时立即调整
- 更精细的速度控制

### 方案4：渐进式速度适应
```c
// 从极慢开始，逐渐适应
Progressive_Speed_Example(sensor);
```

**特点：**
- 从800速度开始
- 逐渐提升到2000
- 出现问题立即降级

## 📊 速度对比表

| 模式 | 直线速度 | 转弯速度 | 直角速度 | 安全等级 |
|------|----------|----------|----------|----------|
| 原始配置 | 3200 | 2400 | 1600 | ⭐⭐ |
| 优化配置 | 2400 | 1600 | 1200 | ⭐⭐⭐ |
| 保守配置 | 2000 | 1400 | 1000 | ⭐⭐⭐⭐ |
| 极保守配置 | 1800 | 1200 | 800 | ⭐⭐⭐⭐⭐ |

## 🔧 参数调整建议

### 如果仍然冲出轨道：
1. **进一步降低速度**
   ```c
   #define TRACK_SQUARE_STRAIGHT_SPEED 2000  // 再降低400
   #define TRACK_SQUARE_CORNER_SPEED   1400  // 再降低200
   ```

2. **增加循环延时**
   ```c
   delay_ms(20);  // 从10ms增加到20ms
   ```

3. **使用更敏感的检测**
   ```c
   #define TRACK_SQUARE_SHARP_TURN_THRESHOLD  1  // 从2降到1
   ```

### 如果速度太慢：
1. **逐步提高速度**
   ```c
   // 先确保不冲出，再逐步提高100-200的速度
   #define TRACK_SQUARE_STRAIGHT_SPEED 2200
   ```

2. **减少循环延时**
   ```c
   delay_ms(12);  // 从15ms减少到12ms
   ```

## 🛠️ 调试步骤

### 第一步：确认基础功能
```c
// 使用最保守配置测试
Ultra_Conservative_Tracking_Example(sensor);
```

### 第二步：观察行为
- 是否还会冲出轨道？
- 转弯是否过于缓慢？
- 直线段是否稳定？

### 第三步：微调参数
```c
// 根据观察结果调整配置
Low_Speed_Config_t config = Get_Ultra_Conservative_Config();
config.straight_speed += 200;  // 适当提高直线速度
Apply_Low_Speed_Config(&config);
```

### 第四步：测试不同配置
```c
// 使用配置切换测试找到最佳参数
Config_Switching_Test_Example(sensor);
```

## 📋 快速选择指南

### 轨道质量好，传感器精度高：
```c
Square_Track_Staged_Speed_Example(sensor);  // 分段控制
```

### 轨道质量一般，安全第一：
```c
Ultra_Conservative_Tracking_Example(sensor);  // 极保守模式
```

### 需要逐步适应轨道：
```c
Progressive_Speed_Example(sensor);  // 渐进式控制
```

### 不确定用哪个：
```c
Square_Track_Ultra_Conservative_Example(sensor);  // 超保守示例
```

## ⚠️ 重要提醒

1. **安全第一**：宁可慢一点，也不要冲出轨道
2. **逐步调整**：不要一次性大幅提高速度
3. **观察测试**：每次调整后都要充分测试
4. **记录参数**：找到最佳参数后记录下来

## 🔍 故障排除

### 问题：使用极保守模式仍然冲出
**解决：**
- 检查传感器是否正常工作
- 确认电机响应是否及时
- 考虑进一步降低速度到1000以下

### 问题：速度太慢，循迹效果差
**解决：**
- 使用渐进式控制逐步提升
- 检查轨道质量是否影响检测
- 调整PID参数提高响应性

### 问题：在某些转弯处仍有问题
**解决：**
- 使用分段控制针对性优化
- 增加转弯检测的敏感度
- 考虑轨道设计是否合理

记住：**慢而稳定比快而失控要好得多！**
