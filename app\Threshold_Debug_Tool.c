/**
 * @file Threshold_Debug_Tool.c
 * @brief 灰度传感器阈值调试工具
 * @details 用于查看、调整和优化灰度传感器的阈值设置
 */

#include "Threshold_Debug_Tool.h"
#include "No_Mcu_Ganv_Grayscale_Sensor_Config.h"
#include "bsp_system.h"

/**
 * @brief 显示当前传感器阈值信息
 * @param sensor 传感器对象指针
 */
void Display_Threshold_Info(No_MCU_Sensor *sensor)
{
    #if TRACK_DEBUG_ENABLE
    // 通过OLED显示阈值信息
    // OLED_Clear();
    // OLED_ShowString(0, 0, "Threshold Info", 12, 1);
    
    for(int i = 0; i < 8; i++) {
        // 显示每个通道的阈值
        // OLED_ShowString(0, 12 + i*8, "Ch", 12, 1);
        // OLED_ShowNum(12, 12 + i*8, i, 1, 12, 1);
        // OLED_ShowString(24, 12 + i*8, "W:", 12, 1);
        // OLED_ShowNum(36, 12 + i*8, sensor->Gray_white[i], 4, 12, 1);
        // OLED_ShowString(72, 12 + i*8, "B:", 12, 1);
        // OLED_ShowNum(84, 12 + i*8, sensor->Gray_black[i], 4, 12, 1);
    }
    // OLED_Refresh();
    #endif
    
    #if TRACK_DEBUG_UART_ENABLE
    // 通过串口输出详细信息
    printf("\n=== 灰度传感器阈值信息 ===\n");
    printf("通道 | 白校准 | 黑校准 | 白阈值 | 黑阈值 | 当前值 | 状态\n");
    printf("-----|--------|--------|--------|--------|--------|------\n");
    
    for(int i = 0; i < 8; i++) {
        char status[10];
        if(sensor->Analog_value[i] > sensor->Gray_white[i]) {
            strcpy(status, "白色");
        } else if(sensor->Analog_value[i] < sensor->Gray_black[i]) {
            strcpy(status, "黑色");
        } else {
            strcpy(status, "灰色");
        }
        
        printf(" %2d  | %6d | %6d | %6d | %6d | %6d | %s\n",
               i, 
               sensor->Calibrated_white[i],
               sensor->Calibrated_black[i],
               sensor->Gray_white[i],
               sensor->Gray_black[i],
               sensor->Analog_value[i],
               status);
    }
    printf("========================\n\n");
    #endif
}

/**
 * @brief 实时监控传感器数值和阈值
 * @param sensor 传感器对象指针
 */
void Real_Time_Threshold_Monitor(No_MCU_Sensor *sensor)
{
    static int monitor_counter = 0;
    
    while(1) {
        // 更新传感器数据
        No_Mcu_Ganv_Sensor_Task_Without_tick(sensor);
        
        monitor_counter++;
        
        // 每100次循环显示一次信息
        if(monitor_counter >= 100) {
            monitor_counter = 0;
            Display_Threshold_Info(sensor);
            
            // 显示数字输出状态
            #if TRACK_DEBUG_ENABLE
            unsigned char digital = Get_Digtal_For_User(sensor);
            // OLED_ShowString(0, 100, "Digital:", 12, 1);
            // OLED_ShowBinary(48, 100, digital, 8, 12, 1);  // 显示二进制
            // OLED_Refresh();
            #endif
            
            #if TRACK_DEBUG_UART_ENABLE
            printf("数字输出: 0x%02X (", Get_Digtal_For_User(sensor));
            for(int i = 7; i >= 0; i--) {
                printf("%d", (Get_Digtal_For_User(sensor) >> i) & 1);
            }
            printf(")\n");
            #endif
        }
        
        delay_ms(10);
    }
}

/**
 * @brief 自动校准传感器阈值
 * @param sensor 传感器对象指针
 * @note 需要手动将传感器放在白色和黑色区域
 */
void Auto_Calibrate_Threshold(No_MCU_Sensor *sensor)
{
    unsigned short white_values[8] = {0};
    unsigned short black_values[8] = {0};
    unsigned short temp_values[8];
    
    #if TRACK_DEBUG_ENABLE
    // OLED_Clear();
    // OLED_ShowString(0, 0, "Auto Calibrate", 12, 1);
    // OLED_Refresh();
    #endif
    
    printf("开始自动校准...\n");
    
    // 第一步：校准白色
    printf("请将传感器放在白色区域，5秒后开始采样...\n");
    delay_ms(5000);
    
    printf("正在采样白色值...\n");
    for(int sample = 0; sample < 50; sample++) {
        No_Mcu_Ganv_Sensor_Task_Without_tick(sensor);
        Get_Anolog_Value(sensor, temp_values);
        
        for(int i = 0; i < 8; i++) {
            white_values[i] += temp_values[i];
        }
        delay_ms(20);
    }
    
    // 计算白色平均值
    for(int i = 0; i < 8; i++) {
        white_values[i] /= 50;
    }
    
    printf("白色校准完成\n");
    
    // 第二步：校准黑色
    printf("请将传感器放在黑线上，5秒后开始采样...\n");
    delay_ms(5000);
    
    printf("正在采样黑色值...\n");
    for(int sample = 0; sample < 50; sample++) {
        No_Mcu_Ganv_Sensor_Task_Without_tick(sensor);
        Get_Anolog_Value(sensor, temp_values);
        
        for(int i = 0; i < 8; i++) {
            black_values[i] += temp_values[i];
        }
        delay_ms(20);
    }
    
    // 计算黑色平均值
    for(int i = 0; i < 8; i++) {
        black_values[i] /= 50;
    }
    
    printf("黑色校准完成\n");
    
    // 第三步：应用校准结果
    No_MCU_Ganv_Sensor_Init(sensor, white_values, black_values);
    
    printf("校准完成！新的阈值：\n");
    Display_Threshold_Info(sensor);
    
    #if TRACK_DEBUG_ENABLE
    // OLED_ShowString(0, 12, "Calibration", 12, 1);
    // OLED_ShowString(0, 24, "Complete!", 12, 1);
    // OLED_Refresh();
    #endif
}

/**
 * @brief 手动调整指定通道的阈值
 * @param sensor 传感器对象指针
 * @param channel 通道号(0-7)
 * @param white_offset 白阈值偏移量
 * @param black_offset 黑阈值偏移量
 */
void Manual_Adjust_Threshold(No_MCU_Sensor *sensor, int channel, int white_offset, int black_offset)
{
    if(channel < 0 || channel >= 8) {
        printf("错误：通道号必须在0-7之间\n");
        return;
    }
    
    // 保存原始值
    unsigned short old_white = sensor->Gray_white[channel];
    unsigned short old_black = sensor->Gray_black[channel];
    
    // 应用偏移
    sensor->Gray_white[channel] += white_offset;
    sensor->Gray_black[channel] += black_offset;
    
    // 检查合理性
    if(sensor->Gray_white[channel] <= sensor->Gray_black[channel]) {
        printf("警告：白阈值不能小于等于黑阈值，自动调整\n");
        sensor->Gray_white[channel] = sensor->Gray_black[channel] + 50;
    }
    
    // 检查范围
    if(sensor->Gray_white[channel] > sensor->Calibrated_white[channel]) {
        printf("警告：白阈值超出校准范围，限制到校准值\n");
        sensor->Gray_white[channel] = sensor->Calibrated_white[channel];
    }
    
    if(sensor->Gray_black[channel] < sensor->Calibrated_black[channel]) {
        printf("警告：黑阈值超出校准范围，限制到校准值\n");
        sensor->Gray_black[channel] = sensor->Calibrated_black[channel];
    }
    
    printf("通道%d阈值调整：\n", channel);
    printf("  白阈值: %d -> %d (偏移: %+d)\n", old_white, sensor->Gray_white[channel], white_offset);
    printf("  黑阈值: %d -> %d (偏移: %+d)\n", old_black, sensor->Gray_black[channel], black_offset);
}

/**
 * @brief 检查阈值设置的合理性
 * @param sensor 传感器对象指针
 * @return 1-合理，0-不合理
 */
int Check_Threshold_Validity(No_MCU_Sensor *sensor)
{
    int valid = 1;
    
    printf("检查阈值合理性：\n");
    
    for(int i = 0; i < 8; i++) {
        // 检查阈值间距
        int diff = sensor->Gray_white[i] - sensor->Gray_black[i];
        if(diff < 100) {
            printf("  通道%d: 阈值间距过小(%d) - 不合理\n", i, diff);
            valid = 0;
        }
        
        // 检查阈值顺序
        if(sensor->Gray_white[i] <= sensor->Gray_black[i]) {
            printf("  通道%d: 白阈值(%d) <= 黑阈值(%d) - 不合理\n", 
                   i, sensor->Gray_white[i], sensor->Gray_black[i]);
            valid = 0;
        }
        
        // 检查是否在校准范围内
        if(sensor->Gray_white[i] > sensor->Calibrated_white[i] ||
           sensor->Gray_black[i] < sensor->Calibrated_black[i]) {
            printf("  通道%d: 阈值超出校准范围 - 警告\n", i);
        }
    }
    
    if(valid) {
        printf("  所有通道阈值设置合理\n");
    }
    
    return valid;
}

/**
 * @brief 阈值优化建议
 * @param sensor 传感器对象指针
 */
void Threshold_Optimization_Advice(No_MCU_Sensor *sensor)
{
    printf("\n=== 阈值优化建议 ===\n");
    
    for(int i = 0; i < 8; i++) {
        int white_cal = sensor->Calibrated_white[i];
        int black_cal = sensor->Calibrated_black[i];
        int white_th = sensor->Gray_white[i];
        int black_th = sensor->Gray_black[i];
        int current = sensor->Analog_value[i];
        
        printf("通道%d:\n", i);
        
        // 分析当前值相对于阈值的位置
        if(current > white_th) {
            printf("  当前检测到白色\n");
        } else if(current < black_th) {
            printf("  当前检测到黑色\n");
        } else {
            printf("  当前在灰色区域\n");
            
            // 给出优化建议
            int to_white = white_th - current;
            int to_black = current - black_th;
            
            if(to_white < to_black) {
                printf("  建议：降低白阈值%d，提高灵敏度\n", to_white/2);
            } else {
                printf("  建议：提高黑阈值%d，提高灵敏度\n", to_black/2);
            }
        }
        
        // 检查阈值分布是否均匀
        int cal_range = white_cal - black_cal;
        int th_range = white_th - black_th;
        
        if(th_range < cal_range * 0.5) {
            printf("  建议：增大阈值间距，提高稳定性\n");
        } else if(th_range > cal_range * 0.8) {
            printf("  建议：减小阈值间距，提高灵敏度\n");
        }
    }
    
    printf("==================\n\n");
}
