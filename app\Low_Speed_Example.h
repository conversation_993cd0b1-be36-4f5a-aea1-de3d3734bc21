/**
 * @file Low_Speed_Example.h
 * @brief 低速循迹示例头文件
 * @details 专门解决冲出轨道问题的超保守控制示例函数声明
 */

#ifndef _LOW_SPEED_EXAMPLE_H
#define _LOW_SPEED_EXAMPLE_H

#include "Low_Speed_Config.h"
#include "No_Mcu_Ganv_Grayscale_Sensor_Config.h"

/**
 * @brief 极保守模式循迹示例
 * @param sensor 传感器对象指针
 * @note 使用最保守的参数，确保不冲出轨道
 */
void Ultra_Conservative_Tracking_Example(No_MCU_Sensor *sensor);

/**
 * @brief 渐进式速度控制示例
 * @param sensor 传感器对象指针
 * @note 从极慢速度开始，逐渐适应轨道
 */
void Progressive_Speed_Example(No_MCU_Sensor *sensor);

/**
 * @brief 配置切换测试示例
 * @param sensor 传感器对象指针
 * @note 在不同配置之间切换，找到最适合的参数
 */
void Config_Switching_Test_Example(No_MCU_Sensor *sensor);

#endif /* _LOW_SPEED_EXAMPLE_H */
