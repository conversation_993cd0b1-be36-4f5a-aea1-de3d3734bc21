/**
 * @file Low_Speed_Config.h
 * @brief 低速循迹专用配置文件
 * @details 专门解决冲出轨道问题的超保守速度配置
 */

#ifndef _LOW_SPEED_CONFIG_H
#define _LOW_SPEED_CONFIG_H

/*************************** 超低速配置 ***************************/
// 极保守速度配置（防止冲出轨道）
#define LOW_SPEED_ULTRA_SLOW        1500    // 超慢速度
#define LOW_SPEED_VERY_SLOW         1800    // 很慢速度
#define LOW_SPEED_SLOW              2000    // 慢速度
#define LOW_SPEED_MODERATE          2200    // 中等偏慢速度

// 正方形轨道超保守配置
#define LOW_SPEED_SQUARE_STRAIGHT   1800    // 直线段极保守速度
#define LOW_SPEED_SQUARE_CORNER     1200    // 转弯段极保守速度
#define LOW_SPEED_SQUARE_SHARP      800     // 直角转弯极保守速度

/*************************** 渐进式速度控制 ***************************/
// 分阶段速度配置
#define LOW_SPEED_STAGE1            1000    // 第一阶段：极慢启动
#define LOW_SPEED_STAGE2            1400    // 第二阶段：缓慢加速
#define LOW_SPEED_STAGE3            1800    // 第三阶段：正常慢速
#define LOW_SPEED_STAGE4            2000    // 第四阶段：最高慢速

// 转弯预减速配置
#define LOW_SPEED_PRE_TURN_RATIO    0.7f    // 预转弯减速比例
#define LOW_SPEED_IN_TURN_RATIO     0.5f    // 转弯中减速比例
#define LOW_SPEED_POST_TURN_RATIO   0.8f    // 转弯后恢复比例

/*************************** 差速控制优化 ***************************/
// 超保守差速配置
#define LOW_SPEED_TURN_DIFF_TINY    300     // 微小差速
#define LOW_SPEED_TURN_DIFF_SMALL   500     // 小差速
#define LOW_SPEED_TURN_DIFF_MEDIUM  700     // 中等差速
#define LOW_SPEED_TURN_DIFF_LARGE   900     // 大差速

// 直角转弯专用差速
#define LOW_SPEED_SHARP_TURN_DIFF   600     // 直角转弯差速
#define LOW_SPEED_SHARP_TURN_RATIO  0.4f    // 直角转弯速度比例

/*************************** 检测阈值优化 ***************************/
// 更敏感的检测阈值
#define LOW_SPEED_TURN_THRESHOLD    1       // 转弯检测阈值（更敏感）
#define LOW_SPEED_SHARP_THRESHOLD   2       // 直角检测阈值
#define LOW_SPEED_LOST_THRESHOLD    3       // 丢线检测阈值

// 检测计数器配置
#define LOW_SPEED_TURN_COUNT        1       // 转弯确认次数
#define LOW_SPEED_SHARP_COUNT       1       // 直角确认次数
#define LOW_SPEED_RECOVERY_COUNT    5       // 恢复计数

/*************************** 时间控制参数 ***************************/
// 循环时间控制
#define LOW_SPEED_LOOP_DELAY        15      // 主循环延时(ms)
#define LOW_SPEED_TURN_DELAY        20      // 转弯时额外延时(ms)
#define LOW_SPEED_RECOVERY_DELAY    10      // 恢复时延时(ms)

// 状态持续时间
#define LOW_SPEED_TURN_DURATION     100     // 转弯状态最小持续时间(ms)
#define LOW_SPEED_STRAIGHT_MIN      50      // 直线状态最小持续时间(ms)

/*************************** 安全限制 ***************************/
// 绝对安全限制
#define LOW_SPEED_ABSOLUTE_MAX      2500    // 绝对最大速度
#define LOW_SPEED_ABSOLUTE_MIN      500     // 绝对最小速度
#define LOW_SPEED_EMERGENCY_STOP    0       // 紧急停止速度

// 速度变化限制
#define LOW_SPEED_MAX_ACCELERATION  200     // 最大加速度(每次循环)
#define LOW_SPEED_MAX_DECELERATION  300     // 最大减速度(每次循环)

/*************************** 预定义配置组合 ***************************/
// 配置组合1：极保守模式
#define LOW_SPEED_CONFIG_ULTRA_CONSERVATIVE \
    .straight_speed = LOW_SPEED_SQUARE_STRAIGHT, \
    .corner_speed = LOW_SPEED_SQUARE_CORNER, \
    .sharp_speed = LOW_SPEED_SQUARE_SHARP, \
    .turn_diff = LOW_SPEED_TURN_DIFF_SMALL, \
    .sharp_ratio = LOW_SPEED_SHARP_TURN_RATIO

// 配置组合2：保守模式
#define LOW_SPEED_CONFIG_CONSERVATIVE \
    .straight_speed = LOW_SPEED_MODERATE, \
    .corner_speed = LOW_SPEED_SLOW, \
    .sharp_speed = LOW_SPEED_VERY_SLOW, \
    .turn_diff = LOW_SPEED_TURN_DIFF_MEDIUM, \
    .sharp_ratio = 0.6f

// 配置组合3：平衡模式
#define LOW_SPEED_CONFIG_BALANCED \
    .straight_speed = TRACK_BASE_SPEED_SLOW, \
    .corner_speed = LOW_SPEED_MODERATE, \
    .sharp_speed = LOW_SPEED_SLOW, \
    .turn_diff = LOW_SPEED_TURN_DIFF_LARGE, \
    .sharp_ratio = 0.7f

/*************************** 配置结构体 ***************************/
typedef struct {
    int straight_speed;     // 直线速度
    int corner_speed;       // 转弯速度
    int sharp_speed;        // 直角转弯速度
    int turn_diff;          // 转弯差速
    float sharp_ratio;      // 直角转弯比例
    int loop_delay;         // 循环延时
    int turn_threshold;     // 转弯检测阈值
} Low_Speed_Config_t;

/*************************** 函数声明 ***************************/
/**
 * @brief 应用低速配置
 * @param config 配置结构体指针
 */
void Apply_Low_Speed_Config(const Low_Speed_Config_t *config);

/**
 * @brief 获取预定义的极保守配置
 * @return 配置结构体
 */
Low_Speed_Config_t Get_Ultra_Conservative_Config(void);

/**
 * @brief 获取预定义的保守配置
 * @return 配置结构体
 */
Low_Speed_Config_t Get_Conservative_Config(void);

/**
 * @brief 获取预定义的平衡配置
 * @return 配置结构体
 */
Low_Speed_Config_t Get_Balanced_Config(void);

#endif /* _LOW_SPEED_CONFIG_H */
