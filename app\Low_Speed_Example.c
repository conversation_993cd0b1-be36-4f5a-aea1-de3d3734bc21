/**
 * @file Low_Speed_Example.c
 * @brief 低速循迹示例实现
 * @details 专门解决冲出轨道问题的超保守控制示例
 */

#include "Low_Speed_Config.h"
#include "Ganway_Optimized.h"
#include "Square_Track_Example.h"
#include "bsp_system.h"

/**
 * @brief 应用低速配置到系统
 */
void Apply_Low_Speed_Config(const Low_Speed_Config_t *config)
{
    // 应用速度配置
    Track_Set_Speed(config->straight_speed);
    
    // 这里可以添加更多配置应用逻辑
    // 例如更新全局配置变量等
}

/**
 * @brief 获取极保守配置
 */
Low_Speed_Config_t Get_Ultra_Conservative_Config(void)
{
    Low_Speed_Config_t config = {
        .straight_speed = LOW_SPEED_SQUARE_STRAIGHT,
        .corner_speed = LOW_SPEED_SQUARE_CORNER,
        .sharp_speed = LOW_SPEED_SQUARE_SHARP,
        .turn_diff = LOW_SPEED_TURN_DIFF_SMALL,
        .sharp_ratio = LOW_SPEED_SHARP_TURN_RATIO,
        .loop_delay = LOW_SPEED_LOOP_DELAY,
        .turn_threshold = LOW_SPEED_TURN_THRESHOLD
    };
    return config;
}

/**
 * @brief 获取保守配置
 */
Low_Speed_Config_t Get_Conservative_Config(void)
{
    Low_Speed_Config_t config = {
        .straight_speed = LOW_SPEED_MODERATE,
        .corner_speed = LOW_SPEED_SLOW,
        .sharp_speed = LOW_SPEED_VERY_SLOW,
        .turn_diff = LOW_SPEED_TURN_DIFF_MEDIUM,
        .sharp_ratio = 0.6f,
        .loop_delay = 12,
        .turn_threshold = LOW_SPEED_TURN_THRESHOLD
    };
    return config;
}

/**
 * @brief 获取平衡配置
 */
Low_Speed_Config_t Get_Balanced_Config(void)
{
    Low_Speed_Config_t config = {
        .straight_speed = TRACK_BASE_SPEED_SLOW,
        .corner_speed = LOW_SPEED_MODERATE,
        .sharp_speed = LOW_SPEED_SLOW,
        .turn_diff = LOW_SPEED_TURN_DIFF_LARGE,
        .sharp_ratio = 0.7f,
        .loop_delay = 10,
        .turn_threshold = 2
    };
    return config;
}

/**
 * @brief 极保守模式循迹示例
 * @param sensor 传感器对象指针
 * @note 使用最保守的参数，确保不冲出轨道
 */
void Ultra_Conservative_Tracking_Example(No_MCU_Sensor *sensor)
{
    unsigned char digital_data;
    unsigned short analog_data[8];
    Track_Control_t *status;
    Low_Speed_Config_t config = Get_Ultra_Conservative_Config();
    
    // 初始化系统
    Track_Init();
    Track_Set_Mode(TRACK_MODE_ADAPTIVE);
    Apply_Low_Speed_Config(&config);
    
    #if TRACK_DEBUG_ENABLE
    // OLED_ShowString(0, 0, "Ultra Safe", 12, 1);
    // OLED_ShowString(0, 12, "Mode", 12, 1);
    // OLED_Refresh();
    #endif
    
    while(1) {
        // 获取传感器数据
        No_Mcu_Ganv_Sensor_Task_Without_tick(sensor);
        digital_data = Get_Digtal_For_User(sensor);
        Get_Anolog_Value(sensor, analog_data);
        
        status = Track_Get_Status();
        
        // 根据状态应用不同的速度
        switch(status->state) {
            case TRACK_STATE_NORMAL:
                Track_Set_Speed(config.straight_speed);
                break;
                
            case TRACK_STATE_TURN_LEFT:
            case TRACK_STATE_TURN_RIGHT:
                Track_Set_Speed(config.corner_speed);
                break;
                
            case TRACK_STATE_LOST:
                Track_Set_Speed(config.sharp_speed);
                break;
                
            default:
                Track_Set_Speed(config.sharp_speed);
                break;
        }
        
        // 执行循迹控制
        Way_Optimized(digital_data, analog_data);
        
        // 使用配置的循环延时
        delay_ms(config.loop_delay);
    }
}

/**
 * @brief 渐进式速度控制示例
 * @param sensor 传感器对象指针
 * @note 从极慢速度开始，逐渐适应轨道
 */
void Progressive_Speed_Example(No_MCU_Sensor *sensor)
{
    unsigned char digital_data;
    unsigned short analog_data[8];
    Track_Control_t *status;
    static int progress_counter = 0;
    static int current_stage = 1;
    int target_speed;
    
    Track_Init();
    Track_Set_Mode(TRACK_MODE_ADAPTIVE);
    Track_Set_Speed(LOW_SPEED_STAGE1);  // 从最慢速度开始
    
    while(1) {
        No_Mcu_Ganv_Sensor_Task_Without_tick(sensor);
        digital_data = Get_Digtal_For_User(sensor);
        Get_Anolog_Value(sensor, analog_data);
        
        status = Track_Get_Status();
        progress_counter++;
        
        // 渐进式速度提升（每500次循环提升一个阶段）
        if(progress_counter >= 500 && status->state == TRACK_STATE_NORMAL) {
            progress_counter = 0;
            if(current_stage < 4) {
                current_stage++;
            }
        }
        
        // 根据阶段设置目标速度
        switch(current_stage) {
            case 1:
                target_speed = LOW_SPEED_STAGE1;
                break;
            case 2:
                target_speed = LOW_SPEED_STAGE2;
                break;
            case 3:
                target_speed = LOW_SPEED_STAGE3;
                break;
            case 4:
                target_speed = LOW_SPEED_STAGE4;
                break;
            default:
                target_speed = LOW_SPEED_STAGE1;
                break;
        }
        
        // 根据当前状态调整速度
        switch(status->state) {
            case TRACK_STATE_NORMAL:
                Track_Set_Speed(target_speed);
                break;
                
            case TRACK_STATE_TURN_LEFT:
            case TRACK_STATE_TURN_RIGHT:
                Track_Set_Speed(target_speed * LOW_SPEED_IN_TURN_RATIO);
                // 转弯时降级
                if(current_stage > 1) {
                    current_stage--;
                    progress_counter = 0;
                }
                break;
                
            case TRACK_STATE_LOST:
                Track_Set_Speed(LOW_SPEED_STAGE1);  // 丢线时回到最慢
                current_stage = 1;
                progress_counter = 0;
                break;
                
            default:
                Track_Set_Speed(target_speed * 0.8f);
                break;
        }
        
        Way_Optimized(digital_data, analog_data);
        
        #if TRACK_DEBUG_ENABLE
        // 每100次循环显示一次状态
        if(progress_counter % 100 == 0) {
            // OLED_ShowString(0, 0, "Stage:", 12, 1);
            // OLED_ShowNum(36, 0, current_stage, 1, 12, 1);
            // OLED_ShowString(0, 12, "Speed:", 12, 1);
            // OLED_ShowNum(36, 12, target_speed, 4, 12, 1);
            // OLED_Refresh();
        }
        #endif
        
        delay_ms(LOW_SPEED_LOOP_DELAY);
    }
}

/**
 * @brief 配置切换测试示例
 * @param sensor 传感器对象指针
 * @note 在不同配置之间切换，找到最适合的参数
 */
void Config_Switching_Test_Example(No_MCU_Sensor *sensor)
{
    unsigned char digital_data;
    unsigned short analog_data[8];
    static int config_mode = 0;
    static int switch_counter = 0;
    Low_Speed_Config_t current_config;
    
    Track_Init();
    Track_Set_Mode(TRACK_MODE_ADAPTIVE);
    
    // 初始使用极保守配置
    current_config = Get_Ultra_Conservative_Config();
    Apply_Low_Speed_Config(&current_config);
    
    while(1) {
        No_Mcu_Ganv_Sensor_Task_Without_tick(sensor);
        digital_data = Get_Digtal_For_User(sensor);
        Get_Anolog_Value(sensor, analog_data);
        
        switch_counter++;
        
        // 每1000次循环切换一次配置
        if(switch_counter >= 1000) {
            switch_counter = 0;
            config_mode = (config_mode + 1) % 3;
            
            switch(config_mode) {
                case 0:
                    current_config = Get_Ultra_Conservative_Config();
                    // OLED_ShowString(0, 0, "Ultra Safe", 12, 1);
                    break;
                case 1:
                    current_config = Get_Conservative_Config();
                    // OLED_ShowString(0, 0, "Conservative", 12, 1);
                    break;
                case 2:
                    current_config = Get_Balanced_Config();
                    // OLED_ShowString(0, 0, "Balanced", 12, 1);
                    break;
            }
            
            Apply_Low_Speed_Config(&current_config);
            // OLED_Refresh();
        }
        
        Way_Optimized(digital_data, analog_data);
        delay_ms(current_config.loop_delay);
    }
}
