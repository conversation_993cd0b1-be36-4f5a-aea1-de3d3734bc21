/**
 * @file Threshold_Debug_Tool.h
 * @brief 灰度传感器阈值调试工具头文件
 * @details 用于查看、调整和优化灰度传感器的阈值设置
 */

#ifndef _THRESHOLD_DEBUG_TOOL_H
#define _THRESHOLD_DEBUG_TOOL_H

#include "No_Mcu_Ganv_Grayscale_Sensor_Config.h"

/**
 * @brief 显示当前传感器阈值信息
 * @param sensor 传感器对象指针
 */
void Display_Threshold_Info(No_MCU_Sensor *sensor);

/**
 * @brief 实时监控传感器数值和阈值
 * @param sensor 传感器对象指针
 */
void Real_Time_Threshold_Monitor(No_MCU_Sensor *sensor);

/**
 * @brief 自动校准传感器阈值
 * @param sensor 传感器对象指针
 * @note 需要手动将传感器放在白色和黑色区域
 */
void Auto_Calibrate_Threshold(No_MCU_Sensor *sensor);

/**
 * @brief 手动调整指定通道的阈值
 * @param sensor 传感器对象指针
 * @param channel 通道号(0-7)
 * @param white_offset 白阈值偏移量
 * @param black_offset 黑阈值偏移量
 */
void Manual_Adjust_Threshold(No_MCU_Sensor *sensor, int channel, int white_offset, int black_offset);

/**
 * @brief 检查阈值设置的合理性
 * @param sensor 传感器对象指针
 * @return 1-合理，0-不合理
 */
int Check_Threshold_Validity(No_MCU_Sensor *sensor);

/**
 * @brief 阈值优化建议
 * @param sensor 传感器对象指针
 */
void Threshold_Optimization_Advice(No_MCU_Sensor *sensor);

#endif /* _THRESHOLD_DEBUG_TOOL_H */
