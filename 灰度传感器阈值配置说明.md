# 灰度传感器阈值配置说明

## 📊 阈值计算原理

您的灰度传感器使用了**双阈值**系统，通过校准的白值和黑值自动计算出两个阈值：

### 阈值计算公式
```c
// 白阈值（偏向白色的分界点）
Gray_white[i] = (Calibrated_white[i] * 2 + Calibrated_black[i]) / 3;

// 黑阈值（偏向黑色的分界点）
Gray_black[i] = (Calibrated_white[i] + Calibrated_black[i] * 2) / 3;
```

### 数学原理
假设校准值：
- **白色校准值**：`Calibrated_white = 3000`
- **黑色校准值**：`Calibrated_black = 1000`

计算结果：
- **白阈值**：`Gray_white = (3000×2 + 1000) / 3 = 2333`
- **黑阈值**：`Gray_black = (3000 + 1000×2) / 3 = 1667`

## 🎯 阈值区间划分

```
黑色区域    |    灰色区域    |    白色区域
0 -------- 1667 -------- 2333 -------- 4095
     黑阈值         白阈值
```

### 判断逻辑
```c
if (adc_value > Gray_white) {
    // 检测到白色（轨道外）
    Digital |= (1 << i);   // 置1
} else if (adc_value < Gray_black) {
    // 检测到黑色（黑线）
    Digital &= ~(1 << i);  // 置0
} else {
    // 灰色区域，保持原状态
}
```

## 📋 典型阈值范围

### 12位ADC（0-4095）
| 环境条件 | 白色校准值 | 黑色校准值 | 白阈值 | 黑阈值 |
|----------|------------|------------|--------|--------|
| 室内光线 | 2800-3500 | 800-1500 | 2367-2833 | 1467-2167 |
| 强光环境 | 3200-3800 | 1200-1800 | 2533-3267 | 1800-2400 |
| 弱光环境 | 2000-2800 | 400-1000 | 1467-2200 | 933-1600 |

### 10位ADC（0-1023）
| 环境条件 | 白色校准值 | 黑色校准值 | 白阈值 | 黑阈值 |
|----------|------------|------------|--------|--------|
| 室内光线 | 700-875 | 200-375 | 533-708 | 367-542 |
| 强光环境 | 800-950 | 300-450 | 633-817 | 450-600 |
| 弱光环境 | 500-700 | 100-250 | 367-550 | 233-400 |

### 8位ADC（0-255）
| 环境条件 | 白色校准值 | 黑色校准值 | 白阈值 | 黑阈值 |
|----------|------------|------------|--------|--------|
| 室内光线 | 175-219 | 50-94 | 133-177 | 92-136 |
| 强光环境 | 200-238 | 75-113 | 158-204 | 113-150 |
| 弱光环境 | 125-175 | 25-63 | 92-138 | 58-100 |

## 🔧 阈值调整方法

### 方法1：重新校准（推荐）
```c
// 1. 将传感器放在白色区域，记录白值
unsigned short white_values[8];
Get_Anolog_Value(sensor, white_values);

// 2. 将传感器放在黑线上，记录黑值
unsigned short black_values[8];
Get_Anolog_Value(sensor, black_values);

// 3. 重新初始化传感器
No_MCU_Ganv_Sensor_Init(sensor, white_values, black_values);
```

### 方法2：手动调整阈值
```c
// 如果需要手动微调阈值
void Adjust_Threshold(No_MCU_Sensor *sensor, int channel, int white_offset, int black_offset)
{
    sensor->Gray_white[channel] += white_offset;
    sensor->Gray_black[channel] += black_offset;
    
    // 确保阈值合理性
    if(sensor->Gray_white[channel] <= sensor->Gray_black[channel]) {
        sensor->Gray_white[channel] = sensor->Gray_black[channel] + 50;
    }
}
```

### 方法3：动态阈值调整
```c
// 根据环境光线动态调整
void Dynamic_Threshold_Adjust(No_MCU_Sensor *sensor)
{
    unsigned short current_values[8];
    Get_Anolog_Value(sensor, current_values);
    
    for(int i = 0; i < 8; i++) {
        // 如果当前值偏离预期，微调阈值
        int mid_value = (sensor->Calibrated_white[i] + sensor->Calibrated_black[i]) / 2;
        
        if(current_values[i] > mid_value + 200) {
            // 环境变亮，提高阈值
            sensor->Gray_white[i] += 10;
            sensor->Gray_black[i] += 5;
        } else if(current_values[i] < mid_value - 200) {
            // 环境变暗，降低阈值
            sensor->Gray_white[i] -= 10;
            sensor->Gray_black[i] -= 5;
        }
    }
}
```

## 🛠️ 调试和检测

### 查看当前阈值
```c
void Print_Current_Thresholds(No_MCU_Sensor *sensor)
{
    for(int i = 0; i < 8; i++) {
        printf("Channel %d: White=%d, Black=%d, Current=%d\n", 
               i, sensor->Gray_white[i], sensor->Gray_black[i], sensor->Analog_value[i]);
    }
}
```

### 阈值合理性检查
```c
int Check_Threshold_Validity(No_MCU_Sensor *sensor)
{
    for(int i = 0; i < 8; i++) {
        // 检查阈值间距是否合理
        int diff = sensor->Gray_white[i] - sensor->Gray_black[i];
        if(diff < 100) {  // 间距太小
            return 0;  // 无效
        }
        
        // 检查阈值是否在合理范围内
        if(sensor->Gray_white[i] > sensor->Calibrated_white[i] ||
           sensor->Gray_black[i] < sensor->Calibrated_black[i]) {
            return 0;  // 超出校准范围
        }
    }
    return 1;  // 有效
}
```

## ⚠️ 常见问题

### 问题1：传感器误判
**原因**：阈值设置不当
**解决**：重新校准或调整阈值间距

### 问题2：对环境光敏感
**原因**：阈值固定，无法适应光线变化
**解决**：使用动态阈值调整

### 问题3：某些通道不工作
**原因**：校准值相等或为0
**解决**：检查传感器硬件，重新校准

## 📝 最佳实践

1. **定期校准**：环境变化时重新校准
2. **多点校准**：在不同位置校准，取平均值
3. **阈值监控**：实时监控阈值有效性
4. **渐进调整**：小幅度调整阈值，避免突变

## 🔍 当前配置查看

要查看您当前的具体阈值，可以在代码中添加：

```c
// 在初始化后添加
printf("传感器阈值配置：\n");
for(int i = 0; i < 8; i++) {
    printf("通道%d: 白阈值=%d, 黑阈值=%d\n", 
           i, sensor.Gray_white[i], sensor.Gray_black[i]);
}
```

这样您就能看到每个通道的具体阈值数值了！
