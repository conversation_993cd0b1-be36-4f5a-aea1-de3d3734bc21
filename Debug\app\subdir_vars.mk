################################################################################
# Automatically-generated file. Do not edit!
################################################################################

SHELL = cmd.exe

# Add inputs and outputs from these tool invocations to the build variables 
C_SRCS += \
../app/Ganway.c \
../app/Ganway_Optimized.c \
../app/Low_Speed_Example.c \
../app/No_Mcu_Ganv_Grayscale_Sensor.c \
../app/Scheduler.c \
../app/Square_Track_Example.c \
../app/Square_Track_Test.c \
../app/Track_Example.c \
../app/encoder.c \
../app/key.c \
../app/motor.c \
../app/ringbuffer.c 

C_DEPS += \
./app/Ganway.d \
./app/Ganway_Optimized.d \
./app/Low_Speed_Example.d \
./app/No_Mcu_Ganv_Grayscale_Sensor.d \
./app/Scheduler.d \
./app/Square_Track_Example.d \
./app/Square_Track_Test.d \
./app/Track_Example.d \
./app/encoder.d \
./app/key.d \
./app/motor.d \
./app/ringbuffer.d 

OBJS += \
./app/Ganway.o \
./app/Ganway_Optimized.o \
./app/Low_Speed_Example.o \
./app/No_Mcu_Ganv_Grayscale_Sensor.o \
./app/Scheduler.o \
./app/Square_Track_Example.o \
./app/Square_Track_Test.o \
./app/Track_Example.o \
./app/encoder.o \
./app/key.o \
./app/motor.o \
./app/ringbuffer.o 

OBJS__QUOTED += \
"app\Ganway.o" \
"app\Ganway_Optimized.o" \
"app\Low_Speed_Example.o" \
"app\No_Mcu_Ganv_Grayscale_Sensor.o" \
"app\Scheduler.o" \
"app\Square_Track_Example.o" \
"app\Square_Track_Test.o" \
"app\Track_Example.o" \
"app\encoder.o" \
"app\key.o" \
"app\motor.o" \
"app\ringbuffer.o" 

C_DEPS__QUOTED += \
"app\Ganway.d" \
"app\Ganway_Optimized.d" \
"app\Low_Speed_Example.d" \
"app\No_Mcu_Ganv_Grayscale_Sensor.d" \
"app\Scheduler.d" \
"app\Square_Track_Example.d" \
"app\Square_Track_Test.d" \
"app\Track_Example.d" \
"app\encoder.d" \
"app\key.d" \
"app\motor.d" \
"app\ringbuffer.d" 

C_SRCS__QUOTED += \
"../app/Ganway.c" \
"../app/Ganway_Optimized.c" \
"../app/Low_Speed_Example.c" \
"../app/No_Mcu_Ganv_Grayscale_Sensor.c" \
"../app/Scheduler.c" \
"../app/Square_Track_Example.c" \
"../app/Square_Track_Test.c" \
"../app/Track_Example.c" \
"../app/encoder.c" \
"../app/key.c" \
"../app/motor.c" \
"../app/ringbuffer.c" 


